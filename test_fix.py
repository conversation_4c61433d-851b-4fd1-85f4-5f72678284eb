#!/usr/bin/env python3

import os
import sys
import django

# Add the kontent directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'kontent'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Test the fix
try:
    from analyze.html_link_files import AnalyzeHtmlLink
    
    # This should work now without passing container parameter
    analyzer = AnalyzeHtmlLink(
        points_rules=10,
        points_quantity=1,
        url="https://example.com",
        duration=60
    )
    
    print("✅ SUCCESS: AnalyzeHtmlLink can be instantiated without container parameter")
    print(f"Analy<PERSON> created: {type(analyzer)}")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    sys.exit(1)
