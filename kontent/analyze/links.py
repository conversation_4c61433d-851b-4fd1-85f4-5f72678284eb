# pylint: disable=cyclic-import
import logging
import os
import re
from urllib.parse import urlparse

from analyze import AnalyzeContent, AnalyzeMsOffice
from analyze.parses import external_source
from custom.keeps_exception_handler import KeepsBadRequestError, KeepsInvalidFileUrl
from custom.youtube_client import YoutubeClient
from di import Container
from django_injector import inject
from utils.vimeo_api_client import VimeoInfoExtractor
from yt_dlp import YoutubeDL

# pylint: disable=no-value-for-parameter
# pylint: disable=no-else-raise
# pylint: disable=unnecessary-dunder-call

FILE_ID_POSITION = 3

logger = logging.getLogger("kontent_log")


class AnalyzeVideoAudioLink(AnalyzeContent):
    @inject
    def __init__(self, points_rules: int, points_quantity: int, url: str, container: Container = None) -> None:
        container = container or Container()
        super().__init__(container)
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._url = url
        self._uploader = container.aws_s3_client()
        self._transcribe_client = container.aws_transcribe_client

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        transcript = None
        analyzed = False
        tags = None
        language = None

        try:
            info = YoutubeDL().extract_info(
                self._url,
                download=False,
                extra_info={"all-subs": True, "write-auto-sub": True},
            )
        except Exception as error:
            if str(error).__contains__("ERROR: Video unavailable"):
                raise KeepsBadRequestError(i18n="video_unavailable", detail="Check if content is available") from error
            elif str(error).__contains__("ERROR: Private video"):
                raise KeepsBadRequestError(i18n="private_video", detail="Video content should be public") from error
            else:
                raise KeepsBadRequestError(i18n="video_link_error", detail="Error to access link information") from error

        summary = info.get("description")
        duration = int(info.get("duration"))
        in_minutes = round(duration / 60 + 0.5)
        points = int(in_minutes / self._points_quantity) * self._points_rules

        return {
            "url": self._url,
            "duration": duration,
            "points": points,
            "tags": tags,
            "summary": summary,
            "transcript": transcript,
            "language": language,
            "analyzed": analyzed,
        }

    def upload_file_to_transcript(self, url_to_download) -> dict:
        regex_youtube = r"^(http(s)??\:\/\/)?(www\.)?((youtube\.com\/watch\?v=)|(youtu.be\/))([^&=%\?]{11})"
        regex_sound_cloud = r"^(https?:\/\/)?(www.)?(m\.)?soundcloud\.com\/[\w\-\.]+(\/)+[\w\-\.]+/?$"

        validations = [
            bool(re.match(regex_youtube, url_to_download)),
            bool(re.match(regex_sound_cloud, url_to_download)),
        ]
        if True not in validations:
            return {"job": None, "analyzed": True}

        try:
            file_local = external_source.download_audio(url_to_download)
            name = file_local.split("/")[-1:][0]
            response = self._uploader.send_file(
                file_name=name,
                file_path=file_local,
                bucket="keeps.transcribe",
                sub_folder="file_to_transcribe",
            )

            job = self._transcribe_client.transcribe_async(file_url=response["url"])
            os.remove(file_local)

            return {
                "job": job["TranscriptionJob"]["TranscriptionJobName"],
                "analyzed": False,
            }
        except Exception as error:
            self._object = url_to_download
            self.logger(method=f"Down/Uploading File | URL {url_to_download}", error=str(error))
            return {"job": None, "analyzed": True}


class YoutubeVideoAnalyzer(AnalyzeVideoAudioLink):
    @inject
    def __init__(self, points_rules: int, points_quantity: int, url: str, container: Container = None) -> None:
        super().__init__(points_rules, points_quantity, url, container)

    def execute(self) -> dict:
        """
        Performs the analysis of a YouTube video.
        :return: A dictionary containing information such as URL, duration, points, tags, summary, transcript, and analysis status.
        """
        youtube = YoutubeClient()

        try:
            info = youtube.info(self._url)
            tags = self.format_youtube_tags(info.get("tags", []))
            language = info.get("language", "en")
            transcript = youtube.transcript(self._url, language)
            duration_seconds = info.get("duration", 0)
            points = self._compute_points(duration_seconds)

            return {
                "url": self._url,
                "duration": duration_seconds,  # Duration should be in seconds
                "points": points,
                "tags": tags,
                "summary": info.get("description", "No description available"),
                "transcript": transcript,
                "language": language,
                "analyzed": True,
            }
        except Exception as e:
            logger.error(f"Error analyzing YouTube video: {e}")
            raise  # Propagating the exception

    def _compute_points(self, duration_seconds):
        """
        Computes points based on the video's duration.
        :param duration_seconds: video duration in seconds.
        :return: the number of computed points.
        """
        if duration_seconds == 0:
            return 0

        duration_minutes = round(float(duration_seconds) / 60 + 0.5)
        return int(duration_minutes / self._points_quantity) * self._points_rules

    def format_youtube_tags(self, tag_list):
        """
        Formats YouTube tags to the pattern used by Keeps and for Elasticsearch storage.
        :param tag_list: list of keyword strings.
        :return: a list of dictionaries containing tags and their relevance.
        """
        return [{"tag": tag, "relevance": 1} for tag in tag_list]


class AnalyzeVimeo(AnalyzeVideoAudioLink):
    @inject
    def __init__(self, points_rules: int, points_quantity: int, url: str) -> None:
        super().__init__(points_rules, points_quantity, url)
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._url = url

    def upload_file_to_transcript(self, url_to_download) -> dict:
        # TODO: Vimeo is blocking video downloads, resume transcription when has find a solid solution
        return {"job": None, "analyzed": True}

    def execute(self) -> dict:
        """
        :return dict(upload, duration, points, transcribe_job):
        """
        info = VimeoInfoExtractor().get_info_content(self._url)
        summary = info.get("description")
        duration = info.get("duration")
        in_minutes = round(duration / 60 + 0.5)
        points = int(in_minutes / self._points_quantity) * self._points_rules

        return {
            "url": self._url,
            "duration": duration,
            "points": points,
            "tags": None,
            "summary": summary,
            "transcript": None,
            "language": None,
            "analyzed": False,
        }


class AnalyzeSoundcloud(AnalyzeVideoAudioLink):
    @inject
    def __init__(self, points_rules: int, points_quantity: int, url: str) -> None:
        super().__init__(points_rules, points_quantity, url)
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._url = url


class AnalyzeGdrive(AnalyzeMsOffice):
    @inject
    def __init__(self, points_rules: int, points_quantity: int, url: str, container: Container = None) -> None:
        container = container or Container()
        super().__init__(file_name="", points_rules=points_rules, points_quantity=points_quantity, file_path="", container=container)
        self._google_drive_client = container.google_drive_client
        self._points_rules = points_rules
        self._points_quantity = points_quantity
        self._file_to_analyze = None
        self._url = url

    def execute(self) -> dict:
        url_parsed = urlparse(self._url)
        try:
            file_id = url_parsed.path.split("/")[FILE_ID_POSITION]
        except IndexError as e:
            raise KeepsInvalidFileUrl() from e
        self._google_drive_client.check_public_document(file_id)
        cleaned_url = "{}://{}{}".format(url_parsed.scheme, url_parsed.hostname, url_parsed.path.replace("/edit", ""))
        return {"url": cleaned_url, "analyzed": False}

    def async_analyzer(self, url: str) -> dict:
        try:
            extensions = {
                "presentation": "pptx",
                "spreadsheets": "xlsx",
                "document": "html",
            }
            url_parsed = urlparse(url)
            url_path = url_parsed.path.split("/")
            self._file_to_analyze = self._google_drive_client.download_file(url_path[3], extensions[url_path[1]])
            process_result = self.process(extensions[url_path[1]])
            return process_result
        except Exception as error:
            self.logger("AnalyzeGdrive | async_analyzer", str(error) + url)
            if self._file_to_analyze and os.path.exists(self._file_to_analyze):
                os.remove(self._file_to_analyze)

            return {"duration": 0, "points": 0, "analyzed": True}
