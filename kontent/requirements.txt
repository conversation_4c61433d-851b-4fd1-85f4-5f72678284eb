aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.12
aiosignal==1.3.2
amqp==5.3.1
anyio==4.9.0
APScheduler==3.11.0
argcomplete==3.6.2
asgiref==3.8.1
astor==0.8.1
astroid==3.3.10
async-property==0.2.2
attrs==25.3.0
beautifulsoup4==4.13.4
billiard==4.2.1
boto3==1.38.35
botocore==1.38.35
Brotli==1.1.0
cachetools==5.5.2
celery==5.5.3
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudconvert==2.1.0
colorlog==6.9.0
coreapi==2.3.3
coreschema==0.0.4
coverage==7.9.0
cryptography==45.0.4
curl_cffi==0.11.3
defusedxml==0.7.1
deprecation==2.1.0
dill==0.4.0
discord-webhook==1.4.1
Django==5.2.3
django-cors-headers==4.7.0
django-filter==25.1
django-nose==1.4.7
django-injector==0.1.1
djangorestframework==3.16.0
docopt==0.6.2
drf-yasg==1.21.10
ecdsa==0.19.1
ecs-logging==2.2.0
elastic-apm==6.23.0
elastic-transport==8.17.1
# ElasticMock==1.8.1 # Removed due to Elasticsearch 8.x compatibility issues
elasticsearch==8.18.1
elasticsearch-dsl==8.18.0
et_xmlfile==2.0.0
exceptiongroup==1.3.0
execnet==2.1.1
flake8==7.2.0
flake8-bugbear==24.12.12
flake8-comprehensions==3.16.0
flake8-html==0.4.3
flake8-mutable==1.2.0
flake8-print==5.0.0
flake8_simplify==0.22.0
freezegun==1.5.2
frozenlist==1.7.0
gevent==25.5.1
google-api-core==2.25.0
google-api-python-client==2.172.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-cloud-core==2.4.3
google-cloud-datastore==2.21.0
google-cloud-speech==2.33.0
google-cloud-storage==3.1.0
google-cloud-vision==3.10.2
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
greenlet==3.2.3
grpcio==1.73.0
grpcio-status==1.73.0
gunicorn==23.0.0
h11==0.16.0
html2text==2025.4.15
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
idna==3.10
inflection==0.5.1
iniconfig==2.1.0
injector==0.22.0
isodate==0.7.2
isort==6.0.1
itypes==1.2.0
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.5.1
jwcrypto==1.5.6
kombu==5.5.4
lazy-object-proxy==1.11.0
lxml==5.4.0
lxml_html_clean==0.4.2
Markdown==3.8
MarkupSafe==3.0.2
mccabe==0.7.0
mock==5.2.0
model-mommy==2.0.0
multidict==6.4.4
mutagen==1.47.0
nltk==3.9.1
nose==1.3.7
numpy==2.3.0
oauth2client==4.1.3
oauthlib==3.2.2
openpyxl==3.1.5
packaging==25.0
pandas==2.3.0
pbr==6.1.1
pdfminer.six==20250506
pillow==11.2.1
platformdirs==4.3.8
pluggy==1.6.0
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.13.0
pycountry==24.6.1
pycparser==2.22
pycryptodomex==3.23.0
pydub==0.25.1
pyflakes==3.3.2
Pygments==2.19.1
PyJWT==2.10.1
pylint==3.3.7
pylint-django==2.6.1
pylint-plugin-utils==0.8.2
pylint_report==2.4.2
PyMuPDF==1.26.1
pyparsing==3.2.3
PyPDF2==3.0.1
pytest==8.4.0
pytest-cov==6.2.1
pytest-django==4.11.1
pytest-mock==3.14.1
pytest-split==0.10.0
pytest-xdist==3.7.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-jose==3.5.0
python-keycloak==5.5.1
python-pptx==1.0.2
pytz==2025.2
PyYAML==6.0.2
redis==6.2.0
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rsa==4.9.1
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
ruff==0.11.13
s3transfer==0.13.0
setuptools==80.9.0
singledispatch==4.1.2
six==1.17.0
slackclient==2.9.4
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
sqlparse==0.5.3
sumy==0.11.0
tomli==2.2.1
tomlkit==0.13.3
tqdm==4.67.1
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
Unidecode==1.4.0
uritemplate==4.2.0
urllib3==2.4.0
vine==5.1.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
xlrd==2.0.1
xlsx2csv==0.8.4
XlsxWriter==3.2.3
xmltodict==0.14.2
yarl==1.20.1
youtube-transcript-api==1.1.0
yt-dlp==2025.6.9
zope.event==5.0
zope.interface==7.2
setuptools<81